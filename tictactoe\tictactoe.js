/**
 * Two-Games Tic Tac <PERSON>e - Single Player and Multiplayer modes
 */
document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements - Mode Selection
    const modeSelector = document.getElementById('mode-selector');
    const singlePlayerBtn = document.getElementById('single-player-btn');
    const multiplayerBtn = document.getElementById('multiplayer-btn');
    const singlePlayerMode = document.getElementById('single-player-mode');
    const multiplayerMode = document.getElementById('multiplayer-mode');
    
    // Single Player Mode Elements
    const difficultySelector = document.getElementById('difficulty');
    const playXBtn = document.getElementById('play-x');
    const playOBtn = document.getElementById('play-o');
    const singleNewGameBtn = document.getElementById('single-new-game-btn');
    const exitSingleBtn = document.getElementById('exit-single-btn');
    const statusMessage = document.getElementById('status-message');
    const playerTurn = document.getElementById('player-turn');
    const aiTurn = document.getElementById('ai-turn');
    const playerScore = document.getElementById('player-score');
    const tiesScore = document.getElementById('ties-score');
    const computerScore = document.getElementById('computer-score');
    const singleBoard = document.getElementById('single-board');
    const singleCells = singleBoard.querySelectorAll('.cell');
    
    // Multiplayer Mode Elements
    const connectionSetup = document.getElementById('connection-setup');
    const createBtn = document.getElementById('create-btn');
    const joinBtn = document.getElementById('join-btn');
    const createGameSection = document.getElementById('create-game');
    const joinGameSection = document.getElementById('join-game');
    const gameCodeDisplay = document.getElementById('game-code');
    const joinCodeInput = document.getElementById('join-code');
    const copyCodeBtn = document.getElementById('copy-code-btn');
    const joinGameBtn = document.getElementById('join-game-btn');
    const createStatusText = document.getElementById('create-status');
    const joinStatusText = document.getElementById('join-status');
    const multiplayerGame = document.getElementById('multiplayer-game');
    const mpStatusMessage = document.getElementById('mp-status');
    const mpPlayerMark = document.getElementById('mp-player-mark');
    const mpPlayerScore = document.getElementById('mp-player-score');
    const mpTiesScore = document.getElementById('mp-ties-score');
    const mpOpponentScore = document.getElementById('mp-opponent-score');
    const mpNewGameBtn = document.getElementById('mp-new-game-btn');
    const mpExitBtn = document.getElementById('mp-exit-btn');
    const mpBoard = document.getElementById('mp-board');
    const mpCells = mpBoard.querySelectorAll('.cell');
    
    // Game state variables - Single Player
    let board = ['', '', '', '', '', '', '', '', ''];
    let currentPlayer = 'X';
    let gameActive = false;
    let playerSymbol = 'X';
    let computerSymbol = 'O';
    let difficulty = 'medium';
    let scores = {
        player: 0,
        ties: 0,
        computer: 0
    };
    
    // Game state variables - Multiplayer
    let mpGameBoard = ['', '', '', '', '', '', '', '', ''];
    let mpCurrentPlayer = 'X';
    let mpGameActive = false;
    let mpPlayerSymbol = 'X';
    let mpScores = {
        player: 0,
        ties: 0,
        opponent: 0
    };
    
    // WebRTC variables
    let peer = null;
    let connection = null;
    
    // Winning combinations
    const winPatterns = [
        [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
        [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
        [0, 4, 8], [2, 4, 6]             // Diagonals
    ];
    
    // Mode Selection Event Listeners
    singlePlayerBtn.addEventListener('click', () => {
        modeSelector.style.display = 'none';
        singlePlayerMode.style.display = 'block';
        initializeSinglePlayerMode();
    });
    
    multiplayerBtn.addEventListener('click', () => {
        modeSelector.style.display = 'none';
        multiplayerMode.style.display = 'block';
    });
    
    // ===== SINGLE PLAYER MODE =====
    
    // Initialize single player mode
    function initializeSinglePlayerMode() {
        resetSinglePlayerBoard();
        gameActive = true;
        updateStatus('Your turn');
        
        // Update the score display
        updateScoreDisplay();
        
        // Set current player based on player's choice
        currentPlayer = playerSymbol;
        updateTurnIndicator();
        
        // If computer goes first, make a move
        if (playerSymbol === 'O') {
            setTimeout(makeComputerMove, 500);
        }
    }
    
    // Event listeners for single player mode
    difficultySelector.addEventListener('change', () => {
        difficulty = difficultySelector.value;
        updateStatus(`Difficulty set to ${difficulty}`);
    });
    
    playXBtn.addEventListener('click', () => {
        playXBtn.classList.add('active');
        playOBtn.classList.remove('active');
        if (playerSymbol !== 'X') {
            playerSymbol = 'X';
            computerSymbol = 'O';
            resetSinglePlayerGame();
        }
    });
    
    playOBtn.addEventListener('click', () => {
        playOBtn.classList.add('active');
        playXBtn.classList.remove('active');
        if (playerSymbol !== 'O') {
            playerSymbol = 'O';
            computerSymbol = 'X';
            resetSinglePlayerGame();
        }
    });
    
    singleNewGameBtn.addEventListener('click', resetSinglePlayerGame);
    
    exitSingleBtn.addEventListener('click', () => {
        singlePlayerMode.style.display = 'none';
        modeSelector.style.display = 'block';
    });
    
    // Add click event listeners to each cell in single player mode
    singleCells.forEach(cell => {
        cell.addEventListener('click', () => handleCellClick(cell));
    });
    
    // Generate a random 6-character game code
    function generateGameCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Removed similar-looking characters
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }
    
    // Initialize PeerJS
    function initializePeer(isCreator) {
        // Create a new Peer with a random ID if creating a game
        // Otherwise create one without an ID for joining
        peer = isCreator 
            ? new Peer(generateGameCode()) 
            : new Peer();
        
        // Handle successful peer creation
        peer.on('open', (id) => {
            if (isCreator) {
                gameId = id;
                gameCodeDisplay.value = gameId;
                statusText.textContent = 'Ready for connection';
            }
        });
        
        // Handle incoming connections (for game creator)
        peer.on('connection', (conn) => {
            connection = conn;
            statusText.textContent = 'Player connected!';
            
            setupConnectionHandlers();
            
            // Start the game once connected
            setTimeout(() => {
                isHost = true;
                playerSymbol = 'X';
                currentPlayer = 'X';
                startGame();
                
                // Send game initialization to the other player
                sendGameData({
                    type: 'game-init',
                    currentPlayer: currentPlayer,
                    peerSymbol: 'O'
                });
            }, 1000);
        });
        
        // Handle errors
        peer.on('error', (err) => {
            console.error('PeerJS error:', err);
            if (err.type === 'peer-unavailable') {
                joinStatusText.textContent = 'Game not found. Check the code and try again.';
            } else {
                const errorElement = isCreator ? statusText : joinStatusText;
                errorElement.textContent = 'Connection error. Please try again.';
            }
        });
    }
    
    // Setup event handlers for the data connection
    function setupConnectionHandlers() {
        connection.on('data', handleGameData);
        
        connection.on('close', () => {
            statusText.textContent = 'Opponent disconnected';
            disableBoard();
        });
        
        connection.on('error', (err) => {
            console.error('Connection error:', err);
            statusText.textContent = 'Connection error';
        });
    }
    
    // Connect to a peer as a client
    function connectToPeer(peerId) {
        joinStatusText.textContent = 'Connecting...';
        
        connection = peer.connect(peerId);
        
        connection.on('open', () => {
            joinStatusText.textContent = 'Connected!';
            setupConnectionHandlers();
            
            // Switch to game view for the client
            setTimeout(() => {
                isHost = false;
                playerSymbol = 'O';
                startGame();
            }, 1000);
        });
    }
    
    // Send game data to the peer
    function sendGameData(data) {
        if (connection && connection.open) {
            connection.send(data);
        }
    }
    
    // Handle incoming game data
    function handleGameData(data) {
        console.log('Received data:', data);
        
        switch (data.type) {
            case 'game-init':
                currentPlayer = data.currentPlayer;
                playerSymbol = data.peerSymbol;
                playerMark.textContent = playerSymbol;
                updateGameStatus();
                break;
                
            case 'make-move':
                gameState[data.cellIndex] = data.symbol;
                document.querySelector(`.cell[data-index="${data.cellIndex}"]`).textContent = data.symbol;
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                checkGameStatus();
                break;
                
            case 'game-reset':
                resetGame();
                break;
        }
    }
    
    // Start the game
    function startGame() {
        // Hide connection setup and show game
        connectionSetup.style.display = 'none';
        gamePlay.style.display = 'block';
        
        // Set player info
        playerMark.textContent = playerSymbol;
        gameActive = true;
        
        updateGameStatus();
        
        // Add event listeners to cells
        cells.forEach(cell => {
            cell.addEventListener('click', handleCellClick);
        });
        
        // Add event listener to reset button
        resetBtn.addEventListener('click', handleResetClick);
        
        // Add event listener to exit button
        exitBtn.addEventListener('click', (e) => {
            e.preventDefault();
            handleExitClick();
        });
    }
    
    // Handle cell click in single player mode
    function handleCellClick(cell) {
        const index = cell.getAttribute('data-index');
        
        // Check if cell is empty and game is active
        if (board[index] !== '' || !gameActive) return;
        
        // Check if it's player's turn
        if (currentPlayer !== playerSymbol) return;
        
        // Make the player's move
        makeMove(index, playerSymbol);
        
        // Check for win or draw
        const result = checkGameStatus();
        
        // If game is over, update scores and status
        if (result === 'win') {
            scores.player++;
            updateScoreDisplay();
            updateStatus('You win!');
            gameActive = false;
        } else if (result === 'draw') {
            scores.ties++;
            updateScoreDisplay();
            updateStatus('Game draw!');
            gameActive = false;
        } else {
            // Computer's turn
            currentPlayer = computerSymbol;
            updateTurnIndicator();
            updateStatus('Computer is thinking...');
            
            // Make computer move after a short delay
            setTimeout(makeComputerMove, 500);
        }
    }
    
    // Make a move on the board
    function makeMove(index, symbol) {
        board[index] = symbol;
        
        // Update the UI
        const cell = singleCells[index];
        cell.classList.add(symbol === 'X' ? 'x-mark' : 'o-mark');
        
        // Switch current player
        currentPlayer = (currentPlayer === 'X') ? 'O' : 'X';
    }
    
    // Computer move algorithm
    function makeComputerMove() {
        if (!gameActive) return;
        
        let index;
        
        // Different difficulty levels
        switch (difficulty) {
            case 'easy':
                index = getRandomMove();
                break;
            case 'medium':
                // 70% chance of optimal move, 30% random
                index = Math.random() < 0.7 ? getBestMove() : getRandomMove();
                break;
            case 'hard':
                // 90% chance of optimal move, 10% random
                index = Math.random() < 0.9 ? getBestMove() : getRandomMove();
                break;
            case 'impossible':
                // Always make the optimal move
                index = getBestMove();
                break;
            default:
                index = getBestMove();
        }
        
        // Make the computer's move
        makeMove(index, computerSymbol);
        
        // Check for win or draw
        const result = checkGameStatus();
        
        // If game is over, update scores and status
        if (result === 'win') {
            scores.computer++;
            updateScoreDisplay();
            updateStatus('Computer wins!');
            highlightWinningCells();
            gameActive = false;
        } else if (result === 'draw') {
            scores.ties++;
            updateScoreDisplay();
            updateStatus('Game draw!');
            gameActive = false;
        } else {
            // Player's turn
            currentPlayer = playerSymbol;
            updateTurnIndicator();
            updateStatus('Your turn');
        }
    }
    
    // Get a random valid move
    function getRandomMove() {
        // Find all empty cells
        const emptyCells = [];
        for (let i = 0; i < board.length; i++) {
            if (board[i] === '') emptyCells.push(i);
        }
        
        // Return a random empty cell
        if (emptyCells.length > 0) {
            return emptyCells[Math.floor(Math.random() * emptyCells.length)];
        }
        return -1;
    }
    
    // Get the best move using minimax algorithm
    function getBestMove() {
        // Create a copy of the board for simulation
        const boardCopy = [...board];
        let bestScore = -Infinity;
        let bestMove;
        
        // Try each empty cell
        for (let i = 0; i < boardCopy.length; i++) {
            if (boardCopy[i] === '') {
                // Make a move on the copy
                boardCopy[i] = computerSymbol;
                
                // Calculate the score for this move
                const score = minimax(boardCopy, 0, false);
                
                // Undo the move
                boardCopy[i] = '';
                
                // Update best move if needed
                if (score > bestScore) {
                    bestScore = score;
                    bestMove = i;
                }
            }
        }
        
        return bestMove;
    }
    
    // Minimax algorithm
    function minimax(board, depth, isMaximizing) {
        // Check if the game is over (win or draw)
        const result = checkGameStatusForBoard(board);
        
        if (result === 'win' && !isMaximizing) {
            return 10 - depth; // AI wins
        }
        if (result === 'win' && isMaximizing) {
            return depth - 10; // Player wins
        }
        if (result === 'draw') {
            return 0; // Draw
        }
        
        if (isMaximizing) {
            // AI's turn (maximize score)
            let bestScore = -Infinity;
            
            for (let i = 0; i < board.length; i++) {
                if (board[i] === '') {
                    board[i] = computerSymbol;
                    const score = minimax(board, depth + 1, false);
                    board[i] = '';
                    bestScore = Math.max(score, bestScore);
                }
            }
            
            return bestScore;
        } else {
            // Player's turn (minimize score)
            let bestScore = Infinity;
            
            for (let i = 0; i < board.length; i++) {
                if (board[i] === '') {
                    board[i] = playerSymbol;
                    const score = minimax(board, depth + 1, true);
                    board[i] = '';
                    bestScore = Math.min(score, bestScore);
                }
            }
            
            return bestScore;
        }
    }
    
    // Check if current board state has a winner or is a draw
    function checkGameStatus() {
        return checkGameStatusForBoard(board);
    }
    
    // Check game status for any board state
    function checkGameStatusForBoard(boardState) {
        // Check for win
        for (const pattern of winPatterns) {
            const [a, b, c] = pattern;
            if (boardState[a] && boardState[a] === boardState[b] && boardState[a] === boardState[c]) {
                return 'win';
            }
        }
        
        // Check for draw
        if (!boardState.includes('')) {
            return 'draw';
        }
        
        // Game still in progress
        return 'ongoing';
    }
    
    // Highlight the winning cells
    function highlightWinningCells() {
        for (const pattern of winPatterns) {
            const [a, b, c] = pattern;
            if (board[a] && board[a] === board[b] && board[a] === board[c]) {
                singleCells[a].classList.add('win');
                singleCells[b].classList.add('win');
                singleCells[c].classList.add('win');
                return;
            }
        }
    }
    
    // Update the turn indicator
    function updateTurnIndicator() {
        if (currentPlayer === playerSymbol) {
            playerTurn.classList.add('active');
            aiTurn.classList.remove('active');
        } else {
            playerTurn.classList.remove('active');
            aiTurn.classList.add('active');
        }
    }
    
    // Update the score display
    function updateScoreDisplay() {
        playerScore.textContent = scores.player;
        tiesScore.textContent = scores.ties;
        computerScore.textContent = scores.computer;
    }
    
    // Update the status message
    function updateStatus(message) {
        statusMessage.textContent = message;
    }
    
    // Reset the single player board
    function resetSinglePlayerBoard() {
        board = ['', '', '', '', '', '', '', '', ''];
        currentPlayer = 'X';
        gameActive = true;
        
        // Clear the UI
        singleCells.forEach(cell => {
            cell.classList.remove('x-mark', 'o-mark', 'win');
        });
    }
    
    // Reset the entire single player game
    function resetSinglePlayerGame() {
        resetSinglePlayerBoard();
        updateTurnIndicator();
        updateStatus('Your turn');
        
        // If computer goes first, make a move
        if (playerSymbol === 'O' && gameActive) {
            currentPlayer = 'X'; // Computer is X
            updateTurnIndicator();
            updateStatus('Computer is thinking...');
            setTimeout(makeComputerMove, 500);
        }
    }
    
    // Disable the board
    function disableBoard() {
        gameActive = false;
        cells.forEach(cell => {
            cell.removeEventListener('click', handleCellClick);
        });
    }
    
    // ===== MULTIPLAYER MODE =====
    
    // Connection setup event listeners
    createBtn.addEventListener('click', () => {
        connectionSetup.style.display = 'block';
        createGameSection.style.display = 'block';
        joinGameSection.style.display = 'none';
        createStatusText.textContent = 'Setting up game...';
        copyCodeBtn.disabled = true;
        initializePeer();
    });
    
    joinBtn.addEventListener('click', () => {
        connectionSetup.style.display = 'block';
        createGameSection.style.display = 'none';
        joinGameSection.style.display = 'block';
        joinStatusText.textContent = 'Setting up connection...';
        joinGameBtn.disabled = true;
        initializePeer();
    });
    
    // Copy button click handler
    copyCodeBtn.addEventListener('click', () => {
        const gameCode = gameCodeDisplay.value.trim();
        
        if (!gameCode) {
            createStatusText.textContent = 'No game code available to copy';
            return;
        }
        
        // Use the Clipboard API if available
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(gameCode)
                .then(() => {
                    createStatusText.textContent = 'Game code copied to clipboard!';
                    setTimeout(() => {
                        createStatusText.textContent = 'Waiting for opponent to join...';
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy:', err);
                    createStatusText.textContent = 'Failed to copy code. Try selecting it manually.';
                    fallbackCopy();
                });
        } else {
            fallbackCopy();
        }
        
        // Fallback for browsers without clipboard API
        function fallbackCopy() {
            try {
                gameCodeDisplay.select();
                const success = document.execCommand('copy');
                if (success) {
                    createStatusText.textContent = 'Game code copied to clipboard!';
                    setTimeout(() => {
                        createStatusText.textContent = 'Waiting for opponent to join...';
                    }, 2000);
                } else {
                    createStatusText.textContent = 'Please select and copy the code manually';
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                createStatusText.textContent = 'Please select and copy the code manually';
            }
        }
    });
    
    // Event listeners for multiplayer game actions
    mpNewGameBtn.addEventListener('click', requestNewMpGame);
    
    mpExitBtn.addEventListener('click', () => {
        if (connection && connection.open && mpGameActive) {
            if (!confirm('Are you sure you want to leave the game? This will disconnect from your opponent.')) {
                return;
            }
            
            // Send disconnect notification to the opponent
            sendGameData({
                type: 'disconnect',
                message: 'Opponent left the game'
            });
        }
        
        // Cleanup and reset UI
        cleanupMpConnection();
        resetMpUI();
    });
    
    // Join game button handler
    joinGameBtn.addEventListener('click', () => {
        const gameCode = joinCodeInput.value.trim().toUpperCase();
        
        if (!gameCode) {
            joinStatusText.textContent = 'Please enter a valid game code';
            return;
        }
        
        joinStatusText.textContent = 'Connecting to game...';
        joinGameBtn.disabled = true;
        
        connectToPeer(gameCode);
    });
    
    // Add click event listeners to each cell in multiplayer mode
    mpCells.forEach(cell => {
        cell.addEventListener('click', () => handleMpCellClick(cell));
    });
    
    // Initialize PeerJS for multiplayer
    function initializePeer() {
        try {
            // Check if creating or joining game
            const isCreatingGame = createGameSection.style.display === 'block';
            
            if (isCreatingGame) {
                // Generate a random 6-character game code
                const gameCode = generateGameCode();
                console.log("Generated game code:", gameCode);
                
                // Create a new peer with the generated ID
                peer = new Peer(gameCode, {
                    debug: 2
                });
            } else {
                // Create a peer with a random ID for joining a game
                peer = new Peer({
                    debug: 2
                });
            }
            
            // Handle successful connection to the PeerJS server
            peer.on('open', (id) => {
                console.log("Connected to PeerJS server with ID:", id);
                
                if (isCreatingGame) {
                    // Display the game code for the host to share
                    gameCodeDisplay.value = id;
                    createStatusText.textContent = 'Game created! Share the code above with your opponent.';
                    copyCodeBtn.disabled = false;
                } else {
                    console.log("Ready to join a game");
                    joinStatusText.textContent = 'Ready to join a game';
                    joinGameBtn.disabled = false;
                }
            });
            
            // Listen for incoming connections if creating a game
            if (isCreatingGame) {
                peer.on('connection', (conn) => {
                    console.log("Incoming connection from:", conn.peer);
                    createStatusText.textContent = 'Opponent is connecting...';
                    
                    // Only accept one connection
                    if (connection && connection.open) {
                        console.log("Rejecting additional connection attempt");
                        conn.close();
                        return;
                    }
                    
                    connection = conn;
                    
                    connection.on('open', () => {
                        console.log("Connection established with:", connection.peer);
                        createStatusText.textContent = 'Opponent connected! Game starting...';
                        setupConnectionHandlers();
                        
                        // Switch to game view
                        setTimeout(() => {
                            createGameSection.style.display = 'none';
                            connectionSetup.style.display = 'none';
                            multiplayerGame.style.display = 'block';
                            
                            // Initialize multiplayer game as X
                            mpPlayerSymbol = 'X';
                            mpPlayerMark.textContent = mpPlayerSymbol;
                            
                            // Start the game
                            initializeMultiplayerGame();
                            
                            // Send initial game data
                            sendGameData({
                                type: 'game-init',
                                playerSymbol: 'O'
                            });
                        }, 1000);
                    });
                });
            }
            
            // Handle connection errors
            peer.on('error', (err) => {
                console.error("PeerJS error:", err);
                
                if (err.type === 'unavailable-id') {
                    // If creating a game and the ID is taken
                    if (isCreatingGame) {
                        createStatusText.textContent = 'Game code already in use. Try again.';
                        // Try again with a new code
                        setTimeout(initializePeer, 1000);
                    }
                } else if (err.type === 'peer-unavailable') {
                    // If joining and the peer is not available
                    joinStatusText.textContent = 'Game not found. Check the code and try again.';
                    joinGameBtn.disabled = false;
                } else {
                    // Generic error handling
                    const statusText = isCreatingGame ? createStatusText : joinStatusText;
                    statusText.textContent = 'Connection error: ' + err.message;
                }
            });
            
            // Handle disconnection from the PeerJS server
            peer.on('disconnected', () => {
                console.log("Disconnected from PeerJS server");
                const statusText = isCreatingGame ? createStatusText : joinStatusText;
                statusText.textContent = 'Disconnected from server. Reconnecting...';
                
                // Try to reconnect
                peer.reconnect();
            });
            
        } catch (e) {
            console.error("Error initializing PeerJS:", e);
            const statusText = createGameSection.style.display === 'block' ? createStatusText : joinStatusText;
            statusText.textContent = 'Connection error: ' + e.message;
        }
    }
    
    // Generate a random game code (6 uppercase alphanumeric characters)
    function generateGameCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Omitting similar looking chars
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // Connect to a peer as a client
    function connectToPeer(peerId) {
        try {
            joinStatusText.textContent = 'Connecting...';
            
            // Clean the peer ID (remove spaces, convert to uppercase)
            const cleanPeerId = peerId.trim().toUpperCase();
            
            if (!cleanPeerId) {
                joinStatusText.textContent = 'Please enter a valid game code';
                return;
            }
            
            console.log("Attempting to connect to peer:", cleanPeerId);
            
            // Add a timeout for connection attempts
            let connectionTimeout = setTimeout(() => {
                if (connection && !connection.open) {
                    joinStatusText.textContent = 'Connection timed out. Host may not be available.';
                    console.error("Connection attempt timed out");
                    // Clean up the failed connection
                    if (connection) {
                        connection.close();
                    }
                }
            }, 10000); // 10 second timeout
            
            connection = peer.connect(cleanPeerId, {
                reliable: true,
                serialization: 'json'
            });
            
            connection.on('open', () => {
                clearTimeout(connectionTimeout);
                console.log("Connected to peer:", connection.peer);
                joinStatusText.textContent = 'Connected! Game starting...';
                setupConnectionHandlers();
                
                // Switch to game view after successful connection
                setTimeout(() => {
                    joinGameSection.style.display = 'none';
                    connectionSetup.style.display = 'none';
                    multiplayerGame.style.display = 'block';
                    
                    // Wait for game init data from host
                    mpStatusMessage.textContent = 'Waiting for game to start...';
                }, 1000);
            });
            
            connection.on('error', (err) => {
                clearTimeout(connectionTimeout);
                console.error("Connection error:", err);
                joinStatusText.textContent = 'Connection error: ' + (err.message || 'Failed to connect');
            });
            
            // Handle case where the connection does not open
            connection.on('close', () => {
                clearTimeout(connectionTimeout);
                if (!multiplayerGame.style.display || multiplayerGame.style.display === 'none') {
                    joinStatusText.textContent = 'Connection closed before game could start';
                }
            });
        } catch (e) {
            console.error("Error connecting to peer:", e);
            joinStatusText.textContent = 'Connection failed: ' + e.message;
        }
    }
    
    // Setup event handlers for the data connection
    function setupConnectionHandlers() {
        if (!connection) {
            console.error("No connection available to setup handlers");
            return;
        }
        
        // Handle incoming game data
        connection.on('data', function(data) {
            console.log("Received data:", data);
            handleGameData(data);
        });
        
        connection.on('close', function() {
            console.log("Connection closed by peer");
            mpStatusMessage.textContent = 'Opponent disconnected';
            mpGameActive = false;
        });
        
        connection.on('error', function(err) {
            console.error('Connection error:', err);
            mpStatusMessage.textContent = 'Connection error: ' + err.message;
        });
    }
    
    // Handle incoming game data
    function handleGameData(data) {
        try {
            console.log('Processing game data:', data);
            
            if (!data || !data.type) {
                console.error("Invalid data received:", data);
                return;
            }
            
            switch (data.type) {
                case 'game-init':
                    console.log("Initializing game as:", data.playerSymbol);
                    mpPlayerSymbol = data.playerSymbol;
                    mpPlayerMark.textContent = mpPlayerSymbol;
                    initializeMultiplayerGame();
                    break;
                    
                case 'move':
                    if (typeof data.index !== 'number') {
                        console.error("Invalid move data:", data);
                        return;
                    }
                    
                    // Make the opponent's move
                    handleOpponentMove(data.index);
                    break;
                    
                case 'new-game':
                    // Reset the game board but keep scores
                    resetMpBoard();
                    mpStatusMessage.textContent = `New game started. ${mpCurrentPlayer === mpPlayerSymbol ? 'Your' : 'Opponent\'s'} turn.`;
                    break;
                    
                case 'disconnect':
                    mpStatusMessage.textContent = data.message || 'Opponent disconnected';
                    mpGameActive = false;
                    break;
                    
                default:
                    console.warn("Unknown data type received:", data.type);
            }
        } catch (e) {
            console.error("Error handling game data:", e);
            mpStatusMessage.textContent = 'Error processing game data';
        }
    }
    
    // Send game data to the peer
    function sendGameData(data) {
        if (connection && connection.open) {
            try {
                console.log("Sending data:", data);
                connection.send(data);
            } catch (e) {
                console.error("Error sending data:", e);
                mpStatusMessage.textContent = 'Error sending move';
            }
        } else {
            console.error("Cannot send data - connection not open");
            mpStatusMessage.textContent = 'Connection lost';
        }
    }
    
    // Initialize the multiplayer game
    function initializeMultiplayerGame() {
        resetMpBoard();
        mpGameActive = true;
        mpCurrentPlayer = 'X'; // X always goes first
        
        // Update UI based on player's symbol
        updateMpStatus();
        updateMpScores();
    }
    
    // Handle cell click in multiplayer mode
    function handleMpCellClick(cell) {
        const index = parseInt(cell.getAttribute('data-index'));
        
        // Check if cell is empty and game is active
        if (mpGameBoard[index] !== '' || !mpGameActive) return;
        
        // Check if it's player's turn
        if (mpCurrentPlayer !== mpPlayerSymbol) return;
        
        // Make the player's move
        makeMpMove(index);
        
        // Send move to opponent
        sendGameData({
            type: 'move',
            index: index
        });
    }
    
    // Make a move on the multiplayer board
    function makeMpMove(index) {
        mpGameBoard[index] = mpCurrentPlayer;
        
        // Update the UI
        const cell = mpCells[index];
        cell.classList.add(mpCurrentPlayer === 'X' ? 'x-mark' : 'o-mark');
        
        // Check for win or draw
        const result = checkMpGameStatus();
        
        // If game is over, update scores and status
        if (result === 'win') {
            if (mpCurrentPlayer === mpPlayerSymbol) {
                mpScores.player++;
                mpStatusMessage.textContent = 'You win!';
            } else {
                mpScores.opponent++;
                mpStatusMessage.textContent = 'Opponent wins!';
            }
            
            updateMpScores();
            highlightMpWinningCells();
            mpGameActive = false;
        } else if (result === 'draw') {
            mpScores.ties++;
            updateMpScores();
            mpStatusMessage.textContent = 'Game draw!';
            mpGameActive = false;
        } else {
            // Switch turn
            mpCurrentPlayer = (mpCurrentPlayer === 'X') ? 'O' : 'X';
            updateMpStatus();
        }
    }
    
    // Handle opponent's move
    function handleOpponentMove(index) {
        // Ensure it's the opponent's turn and the move is valid
        if (mpCurrentPlayer === mpPlayerSymbol || mpGameBoard[index] !== '' || !mpGameActive) {
            console.error("Invalid opponent move:", index);
            return;
        }
        
        // Make the move
        makeMpMove(index);
    }
    
    // Check for win or draw in multiplayer
    function checkMpGameStatus() {
        // Check for win
        for (const pattern of winPatterns) {
            const [a, b, c] = pattern;
            if (mpGameBoard[a] && mpGameBoard[a] === mpGameBoard[b] && mpGameBoard[a] === mpGameBoard[c]) {
                return 'win';
            }
        }
        
        // Check for draw
        if (!mpGameBoard.includes('')) {
            return 'draw';
        }
        
        // Game still in progress
        return 'ongoing';
    }
    
    // Highlight winning cells in multiplayer
    function highlightMpWinningCells() {
        for (const pattern of winPatterns) {
            const [a, b, c] = pattern;
            if (mpGameBoard[a] && mpGameBoard[a] === mpGameBoard[b] && mpGameBoard[a] === mpGameBoard[c]) {
                mpCells[a].classList.add('win');
                mpCells[b].classList.add('win');
                mpCells[c].classList.add('win');
                return;
            }
        }
    }
    
    // Update the multiplayer status message
    function updateMpStatus() {
        if (mpCurrentPlayer === mpPlayerSymbol) {
            mpStatusMessage.textContent = 'Your turn';
        } else {
            mpStatusMessage.textContent = 'Opponent\'s turn';
        }
    }
    
    // Update the multiplayer score display
    function updateMpScores() {
        mpPlayerScore.textContent = mpScores.player;
        mpTiesScore.textContent = mpScores.ties;
        mpOpponentScore.textContent = mpScores.opponent;
    }
    
    // Reset the multiplayer board
    function resetMpBoard() {
        mpGameBoard = ['', '', '', '', '', '', '', '', ''];
        mpCurrentPlayer = 'X';
        mpGameActive = true;
        
        // Clear the UI
        mpCells.forEach(cell => {
            cell.classList.remove('x-mark', 'o-mark', 'win');
        });
    }
    
    // Request a new game in multiplayer
    function requestNewMpGame() {
        if (!connection || !connection.open) {
            mpStatusMessage.textContent = 'No connection to opponent';
            return;
        }
        
        // Send new game request
        sendGameData({
            type: 'new-game'
        });
        
        // Reset the board
        resetMpBoard();
        mpStatusMessage.textContent = `New game started. ${mpCurrentPlayer === mpPlayerSymbol ? 'Your' : 'Opponent\'s'} turn.`;
    }
    
    // Clean up the multiplayer connection
    function cleanupMpConnection() {
        if (connection) {
            connection.close();
            connection = null;
        }
        
        if (peer) {
            peer.destroy();
            peer = null;
        }
    }
    
    // Reset the multiplayer UI
    function resetMpUI() {
        multiplayerGame.style.display = 'none';
        connectionSetup.style.display = 'none';
        multiplayerMode.style.display = 'none';
        modeSelector.style.display = 'block';
        
        // Reset game state
        mpGameBoard = ['', '', '', '', '', '', '', '', ''];
        mpCurrentPlayer = 'X';
        mpGameActive = false;
        mpScores = { player: 0, ties: 0, opponent: 0 };
        
        // Clear form fields
        gameCodeDisplay.value = '';
        joinCodeInput.value = '';
        
        // Reset status messages
        createStatusText.textContent = '';
        joinStatusText.textContent = '';
        mpStatusMessage.textContent = '';
        
        // Clear game elements
        mpCells.forEach(cell => {
            cell.classList.remove('x-mark', 'o-mark', 'win');
        });
    }

    // Make the game responsive
    window.addEventListener('resize', () => {
        // Adjust game board size based on screen width
        const isMobile = window.innerWidth < 768;
        if (isMobile) {
            singleBoard.style.maxWidth = '280px';
            mpBoard.style.maxWidth = '280px';
        } else {
            singleBoard.style.maxWidth = '300px';
            mpBoard.style.maxWidth = '300px';
        }
    });

    // Initial resize trigger
    window.dispatchEvent(new Event('resize'));
}); 