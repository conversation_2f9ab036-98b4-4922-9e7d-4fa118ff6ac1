/* General Styles */
:root {
    --primary-color: #4169E1;
    --secondary-color: #7B68EE;
    --accent-color: #FF6B6B;
    --background-color: #F8F9FA;
    --dark-background: #050A24;
    --light-text: #FFFFFF;
    --dark-text: #333333;
    --border-color: #E1E4E8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--dark-text);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    min-height: calc(100vh - 320px); /* This ensures there's enough height to push footer down */
    flex: 1;
}

.game-title {
    text-align: center;
    color: var(--dark-text);
    margin-bottom: 1.5rem;
    font-size: 2.5rem;
    font-weight: 700;
}

/* Mode Selector Styles */
.mode-selector {
    background-color: #fff;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    min-height: 250px; /* Add minimum height */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mode-title {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: var(--dark-text);
    font-size: 1.8rem;
}

.mode-selector p {
    margin-bottom: 1.5rem;
    color: #666;
}

.mode-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.mode-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    font-size: 1.1rem;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 180px;
}

.primary-btn {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.primary-btn:hover {
    background-color: #3058d2;
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: #F0F2F5;
    color: var(--dark-text);
}

.secondary-btn:hover {
    background-color: #E1E4E8;
    transform: translateY(-2px);
}

.danger-btn {
    background-color: var(--danger-color);
    color: var(--light-text);
}

.danger-btn:hover {
    background-color: #c82333;
}

/* Single Player Mode Styles */
.single-player-mode {
    display: none;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.game-controls {
    margin-bottom: 1.5rem;
}

.game-settings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.difficulty-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.difficulty-selector label {
    font-weight: 500;
    color: var(--dark-text);
}

.difficulty-selector select {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
}

.player-selector {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.player-selector-title {
    font-weight: 500;
    color: var(--dark-text);
}

.player-btn {
    padding: 0.5rem 1.2rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: #F0F2F5;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.player-btn.active {
    background-color: var(--primary-color);
    color: var(--light-text);
    border-color: var(--primary-color);
}

.game-container {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.board-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    justify-content: center;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    width: 300px;
    height: 300px;
    gap: 8px;
    margin: 0 auto;
    background-color: var(--primary-color);
    border-radius: 12px;
    padding: 10px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.cell {
    background-color: #FFFFFF;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--dark-text);
}

.cell:hover {
    background-color: #F0F2F5;
}

.cell.x-mark::before {
    content: "X";
    color: var(--primary-color);
}

.cell.o-mark::before {
    content: "O";
    color: var(--accent-color);
}

.cell.win {
    background-color: #e6f7e6;
}

.game-info {
    flex: 1;
    min-width: 250px;
}

.status-container {
    background-color: #F8F9FA;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.status-message {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--dark-text);
}

.turn-indicator {
    display: flex;
    gap: 1rem;
}

.turn-player {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background-color: #F0F2F5;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.6;
}

.turn-player.active {
    background-color: var(--primary-color);
    color: var(--light-text);
    opacity: 1;
}

.score-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.score-card {
    background-color: #F8F9FA;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    flex: 1;
    margin: 0 0.5rem;
}

.score-card:first-child {
    margin-left: 0;
}

.score-card:last-child {
    margin-right: 0;
}

.score-label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.3rem;
    color: #666;
}

.score-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-text);
}

.game-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.game-btn {
    flex: 1;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Multiplayer Mode Styles */
.multiplayer-mode {
    display: none;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.connection-setup {
    text-align: center;
}

.setup-title {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: var(--dark-text);
}

.setup-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.create-game-section, .join-game-section {
    display: none;
    background-color: #F8F9FA;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.section-title {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--dark-text);
}

.connection-field {
    margin-bottom: 1rem;
}

.game-code {
    display: flex;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.code-input {
    flex: 1;
    padding: 0.8rem 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    letter-spacing: 2px;
    text-align: center;
    text-transform: uppercase;
}

.connection-status {
    font-size: 0.95rem;
    color: #666;
    margin-top: 1rem;
}

.multiplayer-game {
    display: none;
}

.mp-game-container {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.mp-game-info {
    flex: 1;
    min-width: 250px;
}

.mp-status-container {
    background-color: #F8F9FA;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.mp-status-message {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--dark-text);
}

.mp-player-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.mp-player-mark {
    font-weight: 700;
    padding: 0.2rem 0.6rem;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--light-text);
}

/* Responsive Design - Enhanced */
@media (max-width: 768px) {
    .game-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .mode-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .game-settings {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.2rem;
    }
    
    .game-container, .mp-game-container {
        flex-direction: column;
    }
    
    .game-board {
        width: 280px;
        height: 280px;
        margin-bottom: 1.5rem;
    }
    
    .score-container {
        flex-wrap: wrap;
        gap: 0.8rem;
    }
    
    .score-card {
        min-width: 80px;
    }
    
    .game-actions {
        flex-direction: column;
        gap: 0.8rem;
    }
    
    .setup-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .connection-field {
        width: 100%;
    }
    
    .create-game-section, .join-game-section {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 0 0.5rem;
        margin: 1rem auto;
    }
    
    .mode-selector, .single-player-mode, .multiplayer-mode {
        padding: 1rem;
        border-radius: 8px;
    }
    
    .game-board {
        width: 240px;
        height: 240px;
        gap: 5px;
        padding: 8px;
    }
    
    .cell {
        font-size: 2.5rem;
        border-radius: 6px;
    }
    
    .status-container, .mp-status-container {
        padding: 0.8rem;
    }
    
    .turn-indicator {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .turn-player {
        width: 100%;
        justify-content: center;
    }
    
    .code-input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .game-code {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .game-btn {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }
    
    .section-title {
        font-size: 1rem;
    }
    
    /* Full-width buttons */
    .mode-btn, .game-btn, .player-btn {
        width: 100%;
    }
    
    /* Optimize touch targets */
    .player-btn, .game-btn {
        min-height: 44px;
    }
    
    /* Space game cells better for touch */
    .cell {
        min-height: 70px;
    }
    
    /* Make sure score cards are fully visible */
    .score-container {
        justify-content: center;
        gap: 0.5rem;
    }
    
    .score-card {
        margin: 0;
        flex-basis: 30%;
        padding: 0.7rem;
    }
    
    .score-value {
        font-size: 1.5rem;
    }
    
    /* Fix status messages */
    .status-message, .mp-status-message {
        font-size: 1rem;
        text-align: center;
    }
    
    /* Fix header alignment */
    .mode-title, .setup-title {
        font-size: 1.5rem;
    }
}

/* Fix landscape mode issues */
@media (max-height: 480px) and (orientation: landscape) {
    .main-container {
        margin: 0.5rem auto;
    }
    
    .game-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .mode-selector, .single-player-mode, .multiplayer-mode {
        padding: 0.8rem;
    }
    
    .game-container, .mp-game-container {
        flex-direction: row;
        align-items: center;
    }
    
    .game-board {
        width: 200px;
        height: 200px;
        margin-bottom: 0;
    }
    
    .game-info, .mp-game-info {
        max-width: 200px;
    }
    
    .cell {
        font-size: 2rem;
    }
    
    .score-card {
        padding: 0.5rem;
    }
    
    .score-value {
        font-size: 1.2rem;
    }
    
    .game-actions {
        margin-top: 0.5rem;
    }
}

/* Footer adjustments */
footer {
    margin-top: auto; /* Push footer to the bottom */
    width: 100%;
} 