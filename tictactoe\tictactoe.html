<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic Tac <PERSON>e - Two-Games</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="tictactoe.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/peerjs@1.4.7/dist/peerjs.min.js"></script>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1><a href="../../index.html">Two-Games</a></h1>
            </div>
            <ul class="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../games.html" class="active">Games</a></li>
                <li><a href="../../about.html">About</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-container">
        <h1 class="game-title">Tic Tac Toe</h1>
        
        <!-- Mode Selector -->
        <div id="mode-selector" class="mode-selector">
            <h2 class="mode-title">Choose Game Mode</h2>
            <p>Play against the computer or challenge a friend in real-time</p>
            <div class="mode-buttons">
                <button id="single-player-btn" class="mode-btn primary-btn">
                    <i class="fas fa-robot"></i> Single Player
                </button>
                <button id="multiplayer-btn" class="mode-btn secondary-btn">
                    <i class="fas fa-user-friends"></i> Multiplayer
                </button>
            </div>
        </div>
        
        <!-- Single Player Mode -->
        <div id="single-player-mode" class="single-player-mode">
            <div class="game-controls">
                <div class="game-settings">
                    <div class="difficulty-selector">
                        <label for="difficulty">Difficulty:</label>
                        <select id="difficulty">
                            <option value="easy">Easy</option>
                            <option value="medium" selected>Medium</option>
                            <option value="hard">Hard</option>
                            <option value="impossible">Impossible</option>
                        </select>
                    </div>
                    
                    <div class="player-selector">
                        <span class="player-selector-title">Play as:</span>
                        <button id="play-x" class="player-btn active">X</button>
                        <button id="play-o" class="player-btn">O</button>
                    </div>
                </div>
            </div>
            
            <div class="game-container">
                <div class="board-container">
                    <div id="single-board" class="game-board">
                        <div class="cell" data-index="0"></div>
                        <div class="cell" data-index="1"></div>
                        <div class="cell" data-index="2"></div>
                        <div class="cell" data-index="3"></div>
                        <div class="cell" data-index="4"></div>
                        <div class="cell" data-index="5"></div>
                        <div class="cell" data-index="6"></div>
                        <div class="cell" data-index="7"></div>
                        <div class="cell" data-index="8"></div>
                    </div>
                </div>
                
                <div class="game-info">
                    <div class="status-container">
                        <div id="status-message" class="status-message">Choose to begin</div>
                        <div class="turn-indicator">
                            <div class="turn-player active" id="player-turn">
                                <i class="fas fa-user"></i>
                                <span>Your Turn</span>
                            </div>
                            <div class="turn-player" id="ai-turn">
                                <i class="fas fa-robot"></i>
                                <span>Computer Turn</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="score-container">
                        <div class="score-card">
                            <div class="score-label">You (X)</div>
                            <div id="player-score" class="score-value">0</div>
                        </div>
                        <div class="score-card">
                            <div class="score-label">Ties</div>
                            <div id="ties-score" class="score-value">0</div>
                        </div>
                        <div class="score-card">
                            <div class="score-label">Computer (O)</div>
                            <div id="computer-score" class="score-value">0</div>
                        </div>
                    </div>
                    
                    <div class="game-actions">
                        <button id="single-new-game-btn" class="game-btn primary-btn">New Game</button>
                        <button id="exit-single-btn" class="game-btn danger-btn">Exit Game</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Multiplayer Mode -->
        <div id="multiplayer-mode" class="multiplayer-mode">
            <div id="connection-setup" class="connection-setup">
                <h2 class="setup-title">Play Tic Tac Toe Online</h2>
                <div class="setup-buttons">
                    <button id="create-btn" class="game-btn primary-btn">Create a Game</button>
                    <button id="join-btn" class="game-btn secondary-btn">Join a Game</button>
                </div>
                
                <div id="create-game" class="create-game-section">
                    <h3 class="section-title">Share this code with your opponent:</h3>
                    <div class="connection-field">
                        <div class="game-code">
                            <input id="game-code" class="code-input" type="text" readonly>
                            <button id="copy-code-btn" class="game-btn primary-btn"><i class="fas fa-copy"></i> Copy Code</button>
                        </div>
                    </div>
                    <div id="create-status" class="connection-status">Generating game code...</div>
                </div>
                
                <div id="join-game" class="join-game-section">
                    <h3 class="section-title">Enter the game code:</h3>
                    <div class="connection-field">
                        <div class="game-code">
                            <input id="join-code" class="code-input" type="text" placeholder="ENTER CODE" maxlength="6">
                            <button id="join-game-btn" class="game-btn primary-btn">Connect</button>
                        </div>
                    </div>
                    <div id="join-status" class="connection-status">Enter the code provided by your opponent.</div>
                </div>
            </div>
            
            <div id="multiplayer-game" class="multiplayer-game">
                <div class="mp-game-container">
                    <div class="board-container">
                        <div id="mp-board" class="game-board">
                            <div class="cell" data-index="0"></div>
                            <div class="cell" data-index="1"></div>
                            <div class="cell" data-index="2"></div>
                            <div class="cell" data-index="3"></div>
                            <div class="cell" data-index="4"></div>
                            <div class="cell" data-index="5"></div>
                            <div class="cell" data-index="6"></div>
                            <div class="cell" data-index="7"></div>
                            <div class="cell" data-index="8"></div>
                        </div>
                    </div>
                    
                    <div class="mp-game-info">
                        <div class="mp-status-container">
                            <div id="mp-status" class="mp-status-message">Game ready. X goes first.</div>
                            <div id="mp-player-info" class="mp-player-info">
                                <span class="mp-player-info-label">You are playing as:</span>
                                <span id="mp-player-mark" class="mp-player-mark">X</span>
                            </div>
                        </div>
                        
                        <div class="score-container">
                            <div class="score-card">
                                <div class="score-label">You</div>
                                <div id="mp-player-score" class="score-value">0</div>
                            </div>
                            <div class="score-card">
                                <div class="score-label">Ties</div>
                                <div id="mp-ties-score" class="score-value">0</div>
                            </div>
                            <div class="score-card">
                                <div class="score-label">Opponent</div>
                                <div id="mp-opponent-score" class="score-value">0</div>
                            </div>
                        </div>
                        
                        <div class="game-actions">
                            <button id="mp-new-game-btn" class="game-btn primary-btn">New Game</button>
                            <button id="mp-exit-btn" class="game-btn danger-btn">Exit Game</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2>Two-Games</h2>
                <p>Connect and play without barriers</p>
            </div>
            <div class="footer-links">
                <div class="link-group">
                    <h3>Games</h3>
                    <ul>
                        <li><a href="tictactoe.html" class="active">Tic Tac Toe</a></li>
                        <li><a href="../chess/chess.html">Chess</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h3>About</h3>
                    <ul>
                        <li><a href="../../about.html">About Us</a></li>
                        <li><a href="../../about.html#how-it-works">How It Works</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Two-Games. All rights reserved.</p>
        </div>
    </footer>

    <script src="tictactoe.js"></script>
</body>
</html> 