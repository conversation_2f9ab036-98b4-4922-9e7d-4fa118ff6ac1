<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Strategy Board</title>
    <link rel="stylesheet" href="css/styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <style>
        #debug-console {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-width: 400px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Valorant Strategy Board</h1>
            <div class="map-selector">
                <label for="map-select">Select Map:</label>
                <select id="map-select">
                    <option value="Ascent">Ascent</option>
                    <option value="Bind">Bind</option>
                    <option value="Haven">Haven</option>
                    <option value="Split">Split</option>
                    <option value="Icebox">Icebox</option>
                    <option value="Pearl">Pearl</option>
                    <option value="Fracture">Fracture</option>
                    <option value="Lotus">Lotus</option>
                    <option value="Sunset">Sunset</option>
                    <option value="Abyss">Abyss</option>
                </select>
            </div>
        </header>
        
        <main>
            <div class="canvas-container">
                <div class="canvas-wrapper">
                    <canvas id="strategy-canvas"></canvas>
                </div>
            </div>
            
            <div class="sidebar">
                <div class="agents-panel">
                    <h3>Agents</h3>
                    <div class="agent-icons" id="agent-icons">
                        <!-- Agent icons will be loaded here dynamically -->
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <div class="drawing-tools">
                <button id="select-tool" class="tool-btn active" data-tool="select"><span class="tooltip">Select</span>✋</button>
                <button id="pencil-tool" class="tool-btn" data-tool="pencil"><span class="tooltip">Pencil</span>✏️</button>
                <button id="line-tool" class="tool-btn" data-tool="line"><span class="tooltip">Line</span>⟋</button>
                <button id="arrow-tool" class="tool-btn" data-tool="arrow"><span class="tooltip">Arrow</span>→</button>
                <button id="rect-tool" class="tool-btn" data-tool="rect"><span class="tooltip">Rectangle</span>□</button>
                <button id="circle-tool" class="tool-btn" data-tool="circle"><span class="tooltip">Circle</span>○</button>
                <button id="eraser-tool" class="tool-btn" data-tool="eraser"><span class="tooltip">Eraser</span>🧽</button>
                
                <div class="color-picker">
                    <label for="color-select">Color:</label>
                    <input type="color" id="color-select" value="#FF4655">
                </div>
                
                <div class="brush-size">
                    <label for="brush-size">Size:</label>
                    <input type="range" id="brush-size" min="1" max="20" value="3">
                </div>
            </div>
            
            <div class="strategy-controls">
                <button id="clear-btn">Clear</button>
                <button id="undo-btn">Undo</button>
                <button id="redo-btn">Redo</button>
                <input type="text" id="strategy-name" placeholder="Strategy Name">
                <button id="save-btn">Save</button>
                <button id="load-btn">Load</button>
                <button id="debug-btn" onclick="toggleDebug()">Debug</button>
            </div>
        </footer>
    </div>
    
    <div id="load-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Load Strategy</h2>
            <div id="saved-strategies-list"></div>
        </div>
    </div>

    <div id="debug-console"></div>

    <script>
        // Debug console
        function toggleDebug() {
            const debugConsole = document.getElementById('debug-console');
            debugConsole.style.display = debugConsole.style.display === 'none' ? 'block' : 'none';
        }
        
        // Override console.log and console.error
        const originalLog = console.log;
        const originalError = console.error;
        const debugConsole = document.getElementById('debug-console');
        
        console.log = function() {
            // Call original console.log
            originalLog.apply(console, arguments);
            
            // Add to debug console
            const msg = Array.from(arguments).join(' ');
            const logElement = document.createElement('div');
            logElement.textContent = '> ' + msg;
            debugConsole.appendChild(logElement);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };
        
        console.error = function() {
            // Call original console.error
            originalError.apply(console, arguments);
            
            // Add to debug console with error styling
            const msg = Array.from(arguments).join(' ');
            const logElement = document.createElement('div');
            logElement.textContent = '! ' + msg;
            logElement.style.color = '#ff5555';
            debugConsole.appendChild(logElement);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };
    </script>

    <script src="js/app.js"></script>
</body>
</html> 