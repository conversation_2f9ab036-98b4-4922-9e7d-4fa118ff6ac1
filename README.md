# Valorant Strategy Board

An interactive web application for creating and managing Valorant game strategies.

## Features

- **Interactive Map System**:
  - Choose from all Valorant maps (Ascent, Bind, Haven, Split, Icebox, Pearl, Fracture, Lotus, Sunset, and Abyss)
  - Maps serve as the base layer for strategy planning

- **Agent Placement**:
  - Drag and drop agent icons onto the map
  - Rotate and resize agents using controls
  - Color-coded agents for attackers (red) and defenders (blue)

- **Drawing Tools**:
  - Pencil tool for freehand drawing
  - Line and arrow tools for movement paths
  - Rectangle and circle tools for marking areas
  - Multiple colors for different types of annotations
  - Eraser functionality

- **Strategy Management**:
  - Save strategies with custom names
  - Load existing strategies
  - Delete unwanted strategies
  - Undo/redo functionality
  - Clear canvas option

## How to Use

1. **Select a Map**: Choose your desired map from the dropdown at the top.

2. **Choose Side**: Select whether you're planning for attackers or defenders using the radio buttons.

3. **Place Agents**: Click on an agent icon in the sidebar to add it to the map. Drag to position, and use the controls to rotate or resize.

4. **Draw Strategy Elements**:
   - Use the pencil tool for freehand drawing
   - Use line and arrow tools for movement paths
   - Use shape tools (rectangle, circle) to highlight areas
   - Change colors using the color picker
   - Adjust brush size with the slider

5. **Save Your Strategy**:
   - Enter a name for your strategy
   - Click the Save button
   - Your strategy will be stored in the browser's localStorage

6. **Load or Delete Strategies**:
   - Click the Load button to see all saved strategies
   - Click on a strategy name to load it
   - Click the delete (❌) icon to remove a strategy

## Technical Details

- Built with HTML, CSS, and JavaScript
- Uses Fabric.js for canvas manipulation and drawing tools
- Stores strategies in the browser's localStorage
- Responsive design that works on different screen sizes

## Getting Started

1. Clone this repository
2. Open `index.html` in your web browser
3. No server or build process required - this is a client-side application

## Browser Compatibility

Works on modern browsers including:
- Chrome
- Firefox
- Safari
- Edge 